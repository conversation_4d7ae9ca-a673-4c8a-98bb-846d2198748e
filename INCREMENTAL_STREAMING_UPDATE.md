# 增量流式输出功能实现

## 概述
已成功为 `getRuleData` 方法添加了增量流式输出功能。当点击"新增规则"后选择规则时，之前的数据不会被清空，只会流式显示新增的数据，流式完成后再渲染到页面上。

## 主要修改

### 1. 修改 getRuleData 方法签名
添加了 `isIncremental` 参数来控制是否为增量更新：

```typescript
const getRuleData = async (
  type: string,
  ruleDbType?: string,
  RAG_Name?: string,
  isIncremental: boolean = false // 新增参数：是否为增量更新
) => {
```

### 2. 修改流式输出初始化逻辑
在开始流式输出时，根据是否为增量更新决定是否清空文本：

```typescript
// 开始流式输出
setIsStreaming(true);
setShowStreamContent(true);
if (!isIncremental) {
  // 非增量更新时清空文本
  setStreamingText("");
}
```

### 3. 修改流式文本更新逻辑
在 `onMessage` 回调中，根据是否为增量更新决定追加还是替换文本：

```typescript
onMessage: (text: string) => {
  // 流式更新文本
  if (isIncremental) {
    // 增量更新时追加文本
    setStreamingText((prev) => prev + "\n\n" + text);
  } else {
    // 非增量更新时替换文本
    setStreamingText(text);
  }
},
```

### 4. 修改数据处理逻辑
在 `processData` 函数中，增量更新时不立即隐藏流式内容：

```typescript
const processData = () => {
  if (!isIncremental) {
    // 非增量更新时才隐藏流式内容
    setShowStreamContent(false);
  }
  
  // ... 原有的数据处理逻辑
};
```

### 5. 添加延迟隐藏逻辑
在数据处理完成后，为增量更新添加延迟隐藏流式内容的逻辑：

```typescript
if (isValidJSON) {
  // 如果是JSON格式，立即处理数据
  processData();
  // 如果是增量更新，延迟隐藏流式内容
  if (isIncremental) {
    setTimeout(() => {
      setShowStreamContent(false);
    }, 2000);
  }
} else {
  // 如果不是JSON格式，延迟2秒后处理，让用户看到代码块
  setTimeout(() => {
    processData();
    // 增量更新时再延迟隐藏流式内容
    if (isIncremental) {
      setTimeout(() => {
        setShowStreamContent(false);
      }, 1000);
    }
  }, 2000);
}
```

### 6. 修改调用点
在所有新增规则的调用点都传入 `isIncremental: true`：

```typescript
// 自定义知识库
getRuleData("规则匹配", "自定义知识库", newNames.join(","), true);

// 民法典
getRuleData("规则匹配", "民法典", "", true);

// AI生成
getRuleData("规则匹配", "AI生成", "", true);

// 敏感词
getRuleData("规则匹配", "", newNames.join(","), true);
```

## 功能流程

### 初次加载（非增量）
1. 调用 `getRuleData("规则匹配")` （默认 `isIncremental: false`）
2. 清空流式文本，开始流式输出
3. 流式显示完整数据
4. 数据处理完成后立即隐藏流式内容，显示规则列表

### 新增规则（增量）
1. 点击"新增规则"按钮
2. 选择规则类型和具体规则
3. 调用 `getRuleData("规则匹配", ..., true)` （`isIncremental: true`）
4. **不清空**之前的流式文本，在末尾追加新内容
5. 流式显示新增数据
6. 数据处理完成后，**延迟2秒**再隐藏流式内容
7. 显示更新后的规则列表（包含之前的规则 + 新增的规则）

## 用户体验
- **保持连续性**：新增规则时，用户可以看到之前的流式内容和新增的内容
- **清晰分隔**：新增内容通过 `\n\n` 与之前内容分隔
- **适当延迟**：给用户足够时间查看新增的流式内容
- **平滑过渡**：从流式内容到最终页面的过渡更加自然

## 技术要点
- 使用状态管理控制增量更新行为
- 通过文本追加而非替换实现增量显示
- 使用 `setTimeout` 实现延迟隐藏
- 保持原有数据处理逻辑的完整性
