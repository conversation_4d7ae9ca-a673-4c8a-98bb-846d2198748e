# getRuleData 流式输出实现

## 概述
已成功为 `TargetingModule` 组件中的 `getRuleData` 方法添加了流式输出功能。当数据返回时，页面会先显示流式输出的内容，数据返回完成后再展示对应的页面组件。

## 主要修改

### 1. 添加状态管理
在 `src/businessComponents/TargetingModule/index.tsx` 中添加了以下状态：

```typescript
const [streamingText, setStreamingText] = useState<string>(""); // 流式输出的文本
const [isStreaming, setIsStreaming] = useState<boolean>(false); // 是否正在流式输出
const [showStreamContent, setShowStreamContent] = useState<boolean>(false); // 是否显示流式内容
const scrollRef = useRef<HTMLDivElement>(null); // 滚动引用
```

### 2. 导入 StreamTypewriter 组件
```typescript
import StreamTypewriter from "@/component/StreamTypewriter";
```

### 3. 修改 getRuleData 方法
在 `getRuleData` 方法中添加了流式输出逻辑：

- **开始流式输出时**：
  ```typescript
  setIsStreaming(true);
  setShowStreamContent(true);
  setStreamingText("");
  ```

- **流式数据更新**：
  ```typescript
  onMessage: (text: string) => {
    // 流式更新文本
    setStreamingText(text);
  },
  ```

- **流式输出结束**：
  ```typescript
  onFinished: (resultData: any) => {
    setIsStreaming(false);
    
    // 检查是否为JSON格式
    let isValidJSON = false;
    try {
      // 验证JSON格式
      if (type == "规则匹配") {
        if (params.ruleDbType === "民法典") {
          JSON.parse(JSON.parse(resultData).result);
        } else {
          JSON.parse(resultData);
        }
      } else {
        JSON.parse(resultData);
      }
      isValidJSON = true;
    } catch (error) {
      isValidJSON = false;
    }
    
    // 处理数据的函数
    const processData = () => {
      setShowStreamContent(false);
      // ... 原有的数据处理逻辑
    };
    
    if (isValidJSON) {
      // 如果是JSON格式，立即处理
      processData();
    } else {
      // 如果不是JSON格式，延迟2秒后处理，让用户看到代码块
      setTimeout(processData, 2000);
    }
  },
  ```

### 4. 添加UI组件
在规则列表显示区域添加了流式输出组件：

```typescript
{/* 流式输出显示 */}
{showStreamContent && (
  <div 
    ref={scrollRef}
    style={{ 
      marginBottom: token.marginLG,
      maxHeight: "400px",
      overflow: "auto"
    }}
  >
    <StreamTypewriter
      text={streamingText}
      end={!isStreaming}
      charsPerUpdate={5}
      onchange={() => {
        // 自动滚动到底部
        if (scrollRef.current) {
          scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
        }
      }}
    />
  </div>
)}

{/* 规则列表 */}
{!showStreamContent && (
  <List
    itemLayout="horizontal"
    dataSource={rules}
    renderItem={(item) => (
      // ... 原有的列表项渲染逻辑
    )}
  />
)}
```

## 功能特性

### 1. 流式输出
- 使用 `StreamTypewriter` 组件实现打字机效果
- 支持自动滚动到底部
- 可配置字符更新速度（`charsPerUpdate={5}`）

### 2. 智能内容切换
- **JSON数据**：立即切换到页面内容
- **非JSON数据**：延迟2秒显示，让用户先看到代码块内容

### 3. 用户体验优化
- 流式输出时显示加载状态
- 自动滚动确保用户看到最新内容
- 平滑的内容切换过渡

## 使用场景
当调用 `getRuleData` 方法时：
1. 页面开始显示流式输出
2. 数据逐步显示在 StreamTypewriter 组件中
3. 数据返回完成后，根据数据格式决定切换时机
4. 最终显示处理后的页面内容（规则列表等）

## 技术实现要点
- 利用现有的 `useSSEChat` hook 的 `onMessage` 回调
- 通过状态管理控制UI显示切换
- 使用 `setTimeout` 实现延迟切换
- 保持原有数据处理逻辑不变
