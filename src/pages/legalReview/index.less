@import '@/assets/styles/variables.less';
.legal-review-page{
   background: @background;
   min-height: 100vh;
  .legal-review-home{
    .splitting-form{
      width: 800px;
      padding: 20px 50px;
      border-radius: var(--ant-border-radius-lg);
      border: 1px solid #EEEEEE;
      margin:0px auto;
      margin-top: 50px;
      .ant-select-selector{                                                       
        border-color:#EEEEEE;
      }
      .upload-btn{
        width: 120px;
        margin: 0px auto;
        background: @linear-gradient-1;
      }
    }
  }
  // 上传文件样式

  .ant-upload-drag {
    border: 1px dashed #E0EAFF;
    border-radius: 12px;
    padding: 40px 0px;
    background: #FFFFFF;
  }
  .ant-card-body{
    box-shadow: 0px 0px 20px 0px rgba(89, 143, 200, 0.05);
    border-radius: 12px;
    padding: 30px;
  }
  .file-title{
    font-family: Alibaba PuHuiTi 3.0;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
    margin-bottom: 20px;
  }
  .ant-upload-drag-icon {
    margin-bottom: 0;
    font-size: 36px;
    color: #1890ff;
    transition: transform 0.3s ease;
  }

  .ant-upload-text,
  .ant-upload-hint {
    font-size: 14px;
    span:nth-child(1){
      display: block;
      font-family: Alibaba PuHuiTi 3.0;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: #333333;
    }
    span:nth-child(2){
      display: block;
      opacity: 0.6;
      font-family: Alibaba PuHuiTi 3.0;
      font-size: 12px;
      font-weight: normal;
      margin-top: 5px;
    }
  }
  .file-list-contract {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f2f5;
      }

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 12px;
      }
    }
  }
  .info-con-steps{
    height: 79px;
    position: relative;
    padding: 0px 30px 0px 50x;
    background: var(--ant-color-bg-container);
    border-bottom: 1px solid var(--ant-color-border);
    .steps-con{
      width: 900px;
      margin: 0px auto;
    }
    .content-title{
      position:absolute;
      left:50px;
      top: 16px;
      p:nth-child(1){
        font-size: 18px;
        line-height: 26px;
        font-weight: 500;
        color: #333333;
      }
      p:nth-child(2){
        font-size: var(--ant-font-size-sm);
        color: #333333;
        margin-top:6px;
        line-height: var(--line-height-sm);
      }
    }
  }
  .legal-review-footer{
    width: 100%;
    height: 70px;
    background: var(--ant-color-bg-container);
    position: fixed;
    left:0px;
    bottom: 0px;
    .legal-fixed-bottom{
      font-size: var(--ant-font-size-lg);
      font-weight: 500;
      position: absolute;
      right: 20px;
      top: 20px;
      color: #ff4d4f;
    }
  }
}